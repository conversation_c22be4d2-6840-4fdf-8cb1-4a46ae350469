# 🌸 Elegant UI/UX Designer Portfolio

A beautiful, responsive portfolio website designed specifically for UI/UX designers and creative professionals. Built with Next.js, Tailwind CSS, and featuring elegant animations and interactive elements.

## ✨ Features

- **Elegant Design**: Girlish, eye-catching design with soft pastels and sophisticated animations
- **Fully Responsive**: Works perfectly on desktop, tablet, and mobile devices
- **Interactive Elements**: Smooth animations, hover effects, and micro-interactions
- **Modern Tech Stack**: Built with Next.js 15, Tailwind CSS 4, and React 19
- **SEO Optimized**: Proper meta tags, sitemap, and performance optimization
- **Accessible**: Following web accessibility best practices

## 🎨 Sections Included

- **Hero Section**: Eye-catching introduction with animated elements
- **About Me**: Personal story and professional journey
- **Skills**: Interactive skill showcase with progress bars
- **Projects**: Portfolio gallery with case studies
- **Contact**: Working contact form and social links
- **Resume**: Downloadable CV integration

## 🚀 Getting Started

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Run the development server**
   ```bash
   npm run dev
   ```

3. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🎯 Customization Guide

### 1. Personal Information
Update the following files with your information:
- `app/components/Hero.js` - Your name and introduction
- `app/components/About.js` - Your story and background
- `app/components/Contact.js` - Your contact information
- `app/layout.js` - Meta tags and SEO information

### 2. Projects
Edit `app/components/Projects.js` to add your actual projects:
- Replace placeholder project data
- Add your project images to `/public` folder
- Update project descriptions and links

### 3. Skills
Modify `app/components/Skills.js`:
- Update skill categories and proficiency levels
- Add or remove skills based on your expertise

### 4. Colors and Branding
The design system is in `app/globals.css`:
- Customize color palette variables
- Adjust fonts and typography
- Modify animations and effects

### 5. Resume
Replace `public/resume.pdf` with your actual resume file.

### 6. Social Media
Update social media links in:
- `app/components/Hero.js`
- `app/components/Contact.js`
- `app/components/Footer.js`

## 🛠️ Tech Stack

- **Framework**: Next.js 15
- **Styling**: Tailwind CSS 4
- **Language**: JavaScript (React 19)
- **Fonts**: Inter & Dancing Script (Google Fonts)
- **Icons**: Emoji icons for a playful, accessible approach

## 📱 Responsive Design

The portfolio is fully responsive with breakpoints for:
- Mobile: 320px - 768px
- Tablet: 768px - 1024px
- Desktop: 1024px+

## 🎭 Animation Features

- Scroll-triggered animations
- Hover effects and micro-interactions
- Smooth scrolling navigation
- Loading animations
- Floating background elements

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically

**Made with 💖 for the design community**
