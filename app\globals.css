@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');
@import "tailwindcss";

:root {
  --background: #fefdfb;
  --foreground: #2c2c2c;

  /* Elegant sophisticated color palette */
  --primary-50: #faf9f7;
  --primary-100: #f5f3f0;
  --primary-200: #ebe7e0;
  --primary-300: #ddd6cc;
  --primary-400: #ccc0b0;
  --primary-500: #b8a690;
  --primary-600: #a08c70;
  --primary-700: #8a7458;
  --primary-800: #6f5e47;
  --primary-900: #5a4d3a;

  --accent-50: #f8f7f5;
  --accent-100: #f0eeea;
  --accent-200: #e1ddd5;
  --accent-300: #cfc7ba;
  --accent-400: #b8ab98;
  --accent-500: #9d8b73;
  --accent-600: #8a7660;
  --accent-700: #73634f;
  --accent-800: #5e5142;
  --accent-900: #4d4337;

  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;
}

/* Dark mode variables */
[data-theme="dark"] {
  --background: #1a1a1a;
  --foreground: #f5f3f0;

  --primary-50: #2a2520;
  --primary-100: #342e26;
  --primary-200: #453c30;
  --primary-300: #5a4d3a;
  --primary-400: #6f5e47;
  --primary-500: #8a7458;
  --primary-600: #a08c70;
  --primary-700: #b8a690;
  --primary-800: #ccc0b0;
  --primary-900: #ddd6cc;

  --accent-50: #252321;
  --accent-100: #2f2c28;
  --accent-200: #3d3832;
  --accent-300: #4d4337;
  --accent-400: #5e5142;
  --accent-500: #73634f;
  --accent-600: #8a7660;
  --accent-700: #9d8b73;
  --accent-800: #b8ab98;
  --accent-900: #cfc7ba;

  --neutral-50: #171717;
  --neutral-100: #262626;
  --neutral-200: #404040;
  --neutral-300: #525252;
  --neutral-400: #737373;
  --neutral-500: #a3a3a3;
  --neutral-600: #d4d4d4;
  --neutral-700: #e5e5e5;
  --neutral-800: #f5f5f5;
  --neutral-900: #fafafa;
}

@theme inline {
  /* Colors */
  --color-background: var(--background);
  --color-foreground: var(--foreground);

  --color-primary-50: var(--primary-50);
  --color-primary-100: var(--primary-100);
  --color-primary-200: var(--primary-200);
  --color-primary-300: var(--primary-300);
  --color-primary-400: var(--primary-400);
  --color-primary-500: var(--primary-500);
  --color-primary-600: var(--primary-600);
  --color-primary-700: var(--primary-700);
  --color-primary-800: var(--primary-800);
  --color-primary-900: var(--primary-900);

  --color-accent-50: var(--accent-50);
  --color-accent-100: var(--accent-100);
  --color-accent-200: var(--accent-200);
  --color-accent-300: var(--accent-300);
  --color-accent-400: var(--accent-400);
  --color-accent-500: var(--accent-500);
  --color-accent-600: var(--accent-600);
  --color-accent-700: var(--accent-700);
  --color-accent-800: var(--accent-800);
  --color-accent-900: var(--accent-900);

  --color-neutral-50: var(--neutral-50);
  --color-neutral-100: var(--neutral-100);
  --color-neutral-200: var(--neutral-200);
  --color-neutral-300: var(--neutral-300);
  --color-neutral-400: var(--neutral-400);
  --color-neutral-500: var(--neutral-500);
  --color-neutral-600: var(--neutral-600);
  --color-neutral-700: var(--neutral-700);
  --color-neutral-800: var(--neutral-800);
  --color-neutral-900: var(--neutral-900);

  /* Typography */
  --font-display: 'Inter', system-ui, sans-serif;
  --font-body: 'Inter', system-ui, sans-serif;
  --font-script: 'Playfair Display', serif;

  /* Animations */
  --animate-fade-in: fadeIn 0.5s ease-in-out;
  --animate-slide-up: slideUp 0.5s ease-out;
  --animate-slide-down: slideDown 0.5s ease-out;
  --animate-scale-in: scaleIn 0.3s ease-out;
  --animate-bounce-gentle: bounceGentle 2s infinite;
  --animate-float: float 3s ease-in-out infinite;
  --animate-gradient: gradient 3s ease infinite;

  /* Shadows */
  --shadow-elegant: 0 4px 6px -1px rgba(160, 140, 112, 0.1), 0 2px 4px -1px rgba(160, 140, 112, 0.06);
  --shadow-elegant-lg: 0 10px 15px -3px rgba(160, 140, 112, 0.1), 0 4px 6px -2px rgba(160, 140, 112, 0.05);
  --shadow-soft: 0 4px 6px -1px rgba(184, 166, 144, 0.1), 0 2px 4px -1px rgba(184, 166, 144, 0.06);
  --shadow-glow: 0 0 20px rgba(160, 140, 112, 0.2);
  --shadow-luxury: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* Custom animations */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% { transform: translateY(20px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
  0% { transform: translateY(-20px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
  0% { transform: scale(0.9); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes bounceGentle {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes gradient {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Base styles */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.6;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Enhanced typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  letter-spacing: -0.025em;
}

.font-script {
  font-family: 'Playfair Display', serif;
  font-weight: 500;
}

/* Elegant button styles */
.btn-elegant {
  padding: 0.75rem 2rem;
  border-radius: 9999px;
  font-weight: 500;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--accent-600) 100%);
  box-shadow: var(--shadow-elegant);
}

.btn-elegant:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-glow);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom utility classes */
.gradient-elegant {
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--accent-50) 50%, var(--neutral-50) 100%);
}

.gradient-primary {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--accent-600) 100%);
}

.gradient-soft {
  background: linear-gradient(135deg, var(--primary-100) 0%, var(--accent-200) 100%);
}

.gradient-luxury {
  background: linear-gradient(135deg, var(--primary-200) 0%, var(--accent-300) 50%, var(--neutral-200) 100%);
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary-700) 0%, var(--accent-700) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-dark {
  background: linear-gradient(135deg, var(--primary-400) 0%, var(--accent-400) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
