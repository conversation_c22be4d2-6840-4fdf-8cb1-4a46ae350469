@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Dancing+Script:wght@400;500;600;700&display=swap');

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Elegant girlish color palette */
  --primary-50: #fdf2f8;
  --primary-100: #fce7f3;
  --primary-200: #fbcfe8;
  --primary-300: #f9a8d4;
  --primary-400: #f472b6;
  --primary-500: #ec4899;
  --primary-600: #db2777;
  --primary-700: #be185d;
  --primary-800: #9d174d;
  --primary-900: #831843;

  --accent-50: #faf5ff;
  --accent-100: #f3e8ff;
  --accent-200: #e9d5ff;
  --accent-300: #d8b4fe;
  --accent-400: #c084fc;
  --accent-500: #a855f7;
  --accent-600: #9333ea;
  --accent-700: #7c3aed;
  --accent-800: #6b21a8;
  --accent-900: #581c87;

  --rose-50: #fff1f2;
  --rose-100: #ffe4e6;
  --rose-200: #fecdd3;
  --rose-300: #fda4af;
  --rose-400: #fb7185;
  --rose-500: #f43f5e;
  --rose-600: #e11d48;
  --rose-700: #be123c;
  --rose-800: #9f1239;
  --rose-900: #881337;
}

@theme inline {
  /* Colors */
  --color-background: var(--background);
  --color-foreground: var(--foreground);

  --color-primary-50: var(--primary-50);
  --color-primary-100: var(--primary-100);
  --color-primary-200: var(--primary-200);
  --color-primary-300: var(--primary-300);
  --color-primary-400: var(--primary-400);
  --color-primary-500: var(--primary-500);
  --color-primary-600: var(--primary-600);
  --color-primary-700: var(--primary-700);
  --color-primary-800: var(--primary-800);
  --color-primary-900: var(--primary-900);

  --color-accent-50: var(--accent-50);
  --color-accent-100: var(--accent-100);
  --color-accent-200: var(--accent-200);
  --color-accent-300: var(--accent-300);
  --color-accent-400: var(--accent-400);
  --color-accent-500: var(--accent-500);
  --color-accent-600: var(--accent-600);
  --color-accent-700: var(--accent-700);
  --color-accent-800: var(--accent-800);
  --color-accent-900: var(--accent-900);

  --color-rose-50: var(--rose-50);
  --color-rose-100: var(--rose-100);
  --color-rose-200: var(--rose-200);
  --color-rose-300: var(--rose-300);
  --color-rose-400: var(--rose-400);
  --color-rose-500: var(--rose-500);
  --color-rose-600: var(--rose-600);
  --color-rose-700: var(--rose-700);
  --color-rose-800: var(--rose-800);
  --color-rose-900: var(--rose-900);

  /* Typography */
  --font-display: 'Inter', system-ui, sans-serif;
  --font-body: 'Inter', system-ui, sans-serif;
  --font-script: 'Dancing Script', cursive;

  /* Animations */
  --animate-fade-in: fadeIn 0.5s ease-in-out;
  --animate-slide-up: slideUp 0.5s ease-out;
  --animate-slide-down: slideDown 0.5s ease-out;
  --animate-scale-in: scaleIn 0.3s ease-out;
  --animate-bounce-gentle: bounceGentle 2s infinite;
  --animate-float: float 3s ease-in-out infinite;
  --animate-gradient: gradient 3s ease infinite;

  /* Shadows */
  --shadow-elegant: 0 4px 6px -1px rgba(236, 72, 153, 0.1), 0 2px 4px -1px rgba(236, 72, 153, 0.06);
  --shadow-elegant-lg: 0 10px 15px -3px rgba(236, 72, 153, 0.1), 0 4px 6px -2px rgba(236, 72, 153, 0.05);
  --shadow-soft: 0 4px 6px -1px rgba(168, 85, 247, 0.1), 0 2px 4px -1px rgba(168, 85, 247, 0.06);
  --shadow-glow: 0 0 20px rgba(236, 72, 153, 0.3);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* Custom animations */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% { transform: translateY(20px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
  0% { transform: translateY(-20px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
  0% { transform: scale(0.9); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes bounceGentle {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes gradient {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Base styles */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.6;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom utility classes */
.gradient-elegant {
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--accent-50) 50%, #f0f9ff 100%);
}

.gradient-primary {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--accent-500) 100%);
}

.gradient-soft {
  background: linear-gradient(135deg, var(--primary-100) 0%, var(--accent-200) 100%);
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--accent-600) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
