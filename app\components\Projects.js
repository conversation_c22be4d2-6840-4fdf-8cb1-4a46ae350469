'use client';

import { useState, useEffect, useRef } from 'react';

const Projects = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [activeFilter, setActiveFilter] = useState('all');
  const [selectedProject, setSelectedProject] = useState(null);
  const sectionRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  const projects = [
    {
      id: 1,
      title: 'E-Commerce Mobile App',
      category: 'mobile',
      tags: ['UI/UX', 'Mobile', 'E-commerce'],
      description: 'A beautiful and intuitive shopping experience for fashion enthusiasts',
      longDescription: 'Designed a comprehensive mobile shopping experience that increased user engagement by 40% and conversion rates by 25%. The app features personalized recommendations, seamless checkout, and social shopping capabilities.',
      image: '/api/placeholder/400/300',
      technologies: ['Figma', 'Principle', 'User Research'],
      duration: '3 months',
      role: 'Lead UI/UX Designer',
      challenge: 'Creating an intuitive shopping experience that reduces cart abandonment',
      solution: 'Implemented a streamlined checkout process and personalized product recommendations',
      results: ['40% increase in user engagement', '25% higher conversion rate', '60% reduction in cart abandonment'],
      link: '#',
      github: '#'
    },
    {
      id: 2,
      title: 'Healthcare Dashboard',
      category: 'web',
      tags: ['UI/UX', 'Dashboard', 'Healthcare'],
      description: 'Comprehensive patient management system for healthcare professionals',
      longDescription: 'Designed a complex healthcare dashboard that helps doctors manage patient information efficiently. The interface prioritizes critical information while maintaining a clean, accessible design.',
      image: '/api/placeholder/400/300',
      technologies: ['Figma', 'Adobe XD', 'Usability Testing'],
      duration: '4 months',
      role: 'Senior UX Designer',
      challenge: 'Displaying complex medical data in an intuitive, accessible format',
      solution: 'Created a hierarchical information architecture with customizable views',
      results: ['50% faster patient data access', '30% reduction in user errors', '95% user satisfaction'],
      link: '#',
      github: '#'
    },
    {
      id: 3,
      title: 'Learning Platform',
      category: 'web',
      tags: ['UI/UX', 'Education', 'Platform'],
      description: 'Interactive online learning platform for creative professionals',
      longDescription: 'Developed a comprehensive learning platform that makes complex design concepts accessible through interactive lessons, practical exercises, and community features.',
      image: '/api/placeholder/400/300',
      technologies: ['Figma', 'Prototyping', 'User Testing'],
      duration: '5 months',
      role: 'Product Designer',
      challenge: 'Making complex design concepts engaging and easy to understand',
      solution: 'Interactive lessons with hands-on exercises and peer feedback systems',
      results: ['80% course completion rate', '4.8/5 user rating', '200% increase in user retention'],
      link: '#',
      github: '#'
    },
    {
      id: 4,
      title: 'Fitness Tracking App',
      category: 'mobile',
      tags: ['UI/UX', 'Mobile', 'Health'],
      description: 'Motivational fitness app with social features and personalized workouts',
      longDescription: 'Created a fitness app that combines workout tracking with social motivation. Users can share achievements, join challenges, and get personalized workout recommendations.',
      image: '/api/placeholder/400/300',
      technologies: ['Sketch', 'InVision', 'User Research'],
      duration: '3 months',
      role: 'UI/UX Designer',
      challenge: 'Keeping users motivated and engaged with their fitness goals',
      solution: 'Gamification elements and social features to create accountability',
      results: ['70% daily active users', '45% increase in workout completion', '4.7/5 app store rating'],
      link: '#',
      github: '#'
    },
    {
      id: 5,
      title: 'Restaurant Booking System',
      category: 'web',
      tags: ['UI/UX', 'Booking', 'Restaurant'],
      description: 'Elegant reservation system for fine dining establishments',
      longDescription: 'Designed a sophisticated booking system that enhances the dining experience from reservation to payment. Features include table selection, menu preview, and special occasion planning.',
      image: '/api/placeholder/400/300',
      technologies: ['Figma', 'Adobe Creative Suite'],
      duration: '2 months',
      role: 'UI Designer',
      challenge: 'Creating an elegant interface that matches high-end restaurant branding',
      solution: 'Sophisticated visual design with intuitive booking flow',
      results: ['60% increase in online reservations', '35% reduction in no-shows', '90% customer satisfaction'],
      link: '#',
      github: '#'
    },
    {
      id: 6,
      title: 'Creative Portfolio Website',
      category: 'web',
      tags: ['UI/UX', 'Portfolio', 'Creative'],
      description: 'Stunning portfolio website for a creative agency',
      longDescription: 'Built a visually striking portfolio website that showcases creative work through innovative layouts, smooth animations, and immersive storytelling.',
      image: '/api/placeholder/400/300',
      technologies: ['Figma', 'Webflow', 'Animation'],
      duration: '2 months',
      role: 'Creative Director',
      challenge: 'Standing out in a crowded creative market',
      solution: 'Unique visual storytelling with interactive elements',
      results: ['300% increase in client inquiries', '85% longer session duration', 'Featured in design galleries'],
      link: '#',
      github: '#'
    }
  ];

  const filters = [
    { key: 'all', label: 'All Projects', icon: '🎨' },
    { key: 'mobile', label: 'Mobile Apps', icon: '📱' },
    { key: 'web', label: 'Web Design', icon: '💻' },
  ];

  const filteredProjects = activeFilter === 'all' 
    ? projects 
    : projects.filter(project => project.category === activeFilter);

  return (
    <section id="projects" ref={sectionRef} className="py-20 bg-white relative overflow-hidden">
      {/* Background Decorations */}
      <div className="absolute top-0 left-0 w-64 h-64 bg-accent-50 rounded-full -translate-y-32 -translate-x-32 opacity-60"></div>
      <div className="absolute bottom-0 right-0 w-48 h-48 bg-primary-50 rounded-full translate-y-24 translate-x-24 opacity-50"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className={`text-center mb-16 transition-all duration-1000 ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}>
          <h2 className="text-4xl md:text-5xl font-bold text-gradient mb-4">
            Featured Projects
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            A showcase of my design process, problem-solving approach, and creative solutions
          </p>
        </div>

        {/* Filter Tabs */}
        <div className={`flex flex-wrap justify-center mb-12 transition-all duration-1000 delay-200 ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}>
          {filters.map((filter) => (
            <button
              key={filter.key}
              onClick={() => setActiveFilter(filter.key)}
              className={`flex items-center space-x-2 px-6 py-3 m-2 rounded-full font-semibold transition-all duration-300 ${
                activeFilter === filter.key
                  ? 'bg-gradient-primary text-white shadow-glow scale-105'
                  : 'bg-gray-100 text-gray-700 hover:bg-primary-50 hover:text-primary-600 shadow-elegant hover:shadow-elegant-lg'
              }`}
            >
              <span className="text-lg">{filter.icon}</span>
              <span>{filter.label}</span>
            </button>
          ))}
        </div>

        {/* Projects Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProjects.map((project, index) => (
            <div
              key={project.id}
              className={`group cursor-pointer transition-all duration-500 ${
                isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
              }`}
              style={{ transitionDelay: `${index * 100}ms` }}
              onClick={() => setSelectedProject(project)}
            >
              <div className="bg-white rounded-2xl shadow-elegant hover:shadow-elegant-lg transition-all duration-300 overflow-hidden group-hover:scale-105">
                {/* Project Image */}
                <div className="relative h-48 bg-gradient-soft overflow-hidden">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-6xl opacity-50">🎨</div>
                  </div>
                  <div className="absolute inset-0 bg-gradient-primary opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                  <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="bg-white rounded-full p-2 shadow-elegant">
                      <svg className="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Project Info */}
                <div className="p-6">
                  <div className="flex flex-wrap gap-2 mb-3">
                    {project.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-3 py-1 bg-primary-50 text-primary-600 text-xs font-medium rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 mb-2 group-hover:text-primary-600 transition-colors duration-200">
                    {project.title}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {project.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className={`text-center mt-16 transition-all duration-1000 delay-600 ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}>
          <p className="text-lg text-gray-600 mb-6">
            Interested in seeing more of my work or discussing a project?
          </p>
          <a
            href="#contact"
            className="inline-flex items-center bg-gradient-primary text-white px-8 py-4 rounded-full font-semibold hover:shadow-glow transition-all duration-300 hover:scale-105 group"
          >
            <span>Let&apos;s Work Together</span>
            <svg className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </a>
        </div>
      </div>
    </section>
  );
};

export default Projects;
