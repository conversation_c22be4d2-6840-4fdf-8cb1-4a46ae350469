'use client';

import { useState, useEffect } from 'react';

const Hero = () => {
  const [currentRole, setCurrentRole] = useState(0);
  const roles = ['UI/UX Designer', 'Creative Thinker', 'Problem Solver', 'Design Enthusiast'];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentRole((prev) => (prev + 1) % roles.length);
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const scrollToProjects = () => {
    const element = document.querySelector('#projects');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const scrollToContact = () => {
    const element = document.querySelector('#contact');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 gradient-elegant"></div>
      
      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-primary-200 rounded-full opacity-60 animate-float"></div>
      <div className="absolute top-40 right-20 w-16 h-16 bg-accent-200 rounded-full opacity-50 animate-float" style={{ animationDelay: '1s' }}></div>
      <div className="absolute bottom-40 left-20 w-12 h-12 bg-rose-200 rounded-full opacity-70 animate-float" style={{ animationDelay: '2s' }}></div>
      <div className="absolute bottom-20 right-10 w-24 h-24 bg-primary-100 rounded-full opacity-40 animate-float" style={{ animationDelay: '0.5s' }}></div>

      {/* Main Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="animate-fade-in">
          {/* Profile Image Placeholder */}
          <div className="mb-8 flex justify-center">
            <div className="relative">
              <div className="w-48 h-48 bg-gradient-primary rounded-full flex items-center justify-center shadow-elegant-lg hover:shadow-glow transition-all duration-300 hover:scale-105">
                <div className="w-44 h-44 bg-white rounded-full flex items-center justify-center">
                  <svg className="w-24 h-24 text-primary-400" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                  </svg>
                </div>
              </div>
              {/* Decorative ring */}
              <div className="absolute -inset-4 border-2 border-primary-200 rounded-full animate-bounce-gentle"></div>
            </div>
          </div>

          {/* Greeting */}
          <div className="mb-6">
            <p className="text-lg text-gray-600 mb-2 animate-slide-down">Hello, I'm</p>
            <h1 className="text-5xl md:text-7xl font-bold text-gradient mb-4 animate-slide-up">
              Your Name
            </h1>
            <div className="h-12 flex items-center justify-center">
              <p className="text-2xl md:text-3xl text-gray-700 font-medium">
                A passionate{' '}
                <span className="text-gradient font-bold transition-all duration-500 animate-scale-in">
                  {roles[currentRole]}
                </span>
              </p>
            </div>
          </div>

          {/* Description */}
          <div className="mb-8 animate-fade-in" style={{ animationDelay: '0.5s' }}>
            <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              I create beautiful, user-centered digital experiences that solve real problems.
              With a passion for elegant design and intuitive interfaces, I bring ideas to life
              through thoughtful UI/UX design.
            </p>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center animate-slide-up" style={{ animationDelay: '0.7s' }}>
            <button
              onClick={scrollToProjects}
              className="bg-gradient-primary text-white px-8 py-4 rounded-full font-semibold text-lg hover:shadow-glow transition-all duration-300 hover:scale-105 group"
            >
              View My Work
              <span className="ml-2 group-hover:translate-x-1 transition-transform duration-200 inline-block">→</span>
            </button>
            <button
              onClick={scrollToContact}
              className="border-2 border-primary-500 text-primary-600 px-8 py-4 rounded-full font-semibold text-lg hover:bg-primary-50 transition-all duration-300 hover:scale-105"
            >
              Let&apos;s Connect
            </button>
          </div>

          {/* Social Links */}
          <div className="mt-12 flex justify-center space-x-6 animate-fade-in" style={{ animationDelay: '1s' }}>
            {[
              { name: 'LinkedIn', icon: '💼', href: '#' },
              { name: 'Dribbble', icon: '🎨', href: '#' },
              { name: 'Behance', icon: '🎭', href: '#' },
              { name: 'GitHub', icon: '💻', href: '#' },
            ].map((social) => (
              <a
                key={social.name}
                href={social.href}
                className="w-12 h-12 bg-white rounded-full flex items-center justify-center shadow-elegant hover:shadow-elegant-lg transition-all duration-300 hover:scale-110 hover:-translate-y-1 group"
                title={social.name}
              >
                <span className="text-xl group-hover:scale-110 transition-transform duration-200">
                  {social.icon}
                </span>
              </a>
            ))}
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce-gentle">
        <div className="w-6 h-10 border-2 border-primary-400 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-primary-400 rounded-full mt-2 animate-bounce-gentle"></div>
        </div>
        <p className="text-sm text-gray-500 mt-2">Scroll down</p>
      </div>
    </section>
  );
};

export default Hero;
