import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: "Hemini - Elegant UI/UX Designer Portfolio",
  description: "Sophisticated UI/UX designer creating elegant, user-centered digital experiences. View my portfolio of luxury design solutions and strategic case studies.",
  keywords: "UI/UX Designer, Portfolio, Web Design, Mobile App Design, User Experience, User Interface, Elegant Design, Luxury Design",
  author: "Hemini",
  viewport: "width=device-width, initial-scale=1",
  openGraph: {
    title: "Hemini - Elegant UI/UX Designer Portfolio",
    description: "Sophisticated UI/UX designer creating elegant, user-centered digital experiences.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Hemini - Elegant UI/UX Designer Portfolio",
    description: "Sophisticated UI/UX designer creating elegant, user-centered digital experiences.",
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased font-body`}
      >
        {children}
      </body>
    </html>
  );
}
