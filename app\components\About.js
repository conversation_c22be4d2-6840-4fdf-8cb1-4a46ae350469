'use client';

import { useState, useEffect, useRef } from 'react';

const About = () => {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  const highlights = [
    { number: '3+', label: 'Years Experience', icon: '⭐' },
    { number: '50+', label: 'Projects Completed', icon: '🎨' },
    { number: '100%', label: 'Client Satisfaction', icon: '💖' },
    { number: '24/7', label: 'Creative Thinking', icon: '💡' },
  ];

  const values = [
    {
      title: 'User-Centered Design',
      description: 'Every design decision is made with the user in mind, ensuring intuitive and delightful experiences.',
      icon: '👥',
    },
    {
      title: 'Attention to Detail',
      description: 'I believe that great design lies in the details - from micro-interactions to perfect typography.',
      icon: '🔍',
    },
    {
      title: 'Continuous Learning',
      description: 'Staying updated with the latest design trends and technologies to deliver cutting-edge solutions.',
      icon: '📚',
    },
    {
      title: 'Collaborative Spirit',
      description: 'Working closely with teams and clients to bring visions to life through effective communication.',
      icon: '🤝',
    },
  ];

  return (
    <section id="about" ref={sectionRef} className="py-20 bg-white dark:bg-neutral-900 relative overflow-hidden transition-colors duration-300">
      {/* Background Decorations */}
      <div className="absolute top-0 right-0 w-64 h-64 bg-primary-50 dark:bg-primary-900 rounded-full -translate-y-32 translate-x-32 opacity-30"></div>
      <div className="absolute bottom-0 left-0 w-48 h-48 bg-accent-50 dark:bg-accent-900 rounded-full translate-y-24 -translate-x-24 opacity-25"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className={`text-center mb-16 transition-all duration-1000 ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}>
          <h2 className="text-4xl md:text-5xl font-bold text-gradient mb-4">
            About Me
          </h2>
          <p className="text-xl text-gray-600 dark:text-neutral-400 max-w-3xl mx-auto">
            Passionate about creating sophisticated digital experiences that elevate brands and drive results
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Column - Story */}
          <div className={`transition-all duration-1000 delay-200 ${
            isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-10'
          }`}>
            <div className="space-y-6">
              <h3 className="text-3xl font-bold text-gray-800 dark:text-neutral-200 mb-6">
                My Design Journey
              </h3>

              <div className="prose prose-lg text-gray-600 dark:text-neutral-400">
                <p>
                  Hi there! I&apos;m a passionate UI/UX designer who believes that great design
                  has the power to transform how people interact with technology. My journey
                  began with a fascination for the intersection of art, psychology, and technology.
                </p>
                
                <p>
                  I specialize in creating user-centered designs that not only look beautiful 
                  but also solve real problems. From wireframes to high-fidelity prototypes, 
                  I enjoy every step of the design process and the collaborative journey of 
                  bringing ideas to life.
                </p>
                
                <p>
                  When I&apos;m not designing, you can find me exploring the latest design trends,
                  sketching ideas in my notebook, or experimenting with new creative tools.
                  I believe that inspiration can come from anywhere - nature, architecture,
                  or even a beautiful sunset.
                </p>
              </div>

              {/* Download Resume Button */}
              <div className="pt-6">
                <a
                  href="/resume.pdf"
                  download
                  className="inline-flex items-center bg-gradient-primary text-white px-6 py-3 rounded-full font-semibold hover:shadow-glow transition-all duration-300 hover:scale-105 group"
                >
                  <span>Download Resume</span>
                  <svg className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </a>
              </div>
            </div>
          </div>

          {/* Right Column - Stats & Values */}
          <div className={`transition-all duration-1000 delay-400 ${
            isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'
          }`}>
            {/* Highlights */}
            <div className="grid grid-cols-2 gap-6 mb-12">
              {highlights.map((item, index) => (
                <div
                  key={index}
                  className="bg-gradient-soft p-6 rounded-2xl text-center hover:shadow-elegant transition-all duration-300 hover:scale-105"
                >
                  <div className="text-3xl mb-2">{item.icon}</div>
                  <div className="text-3xl font-bold text-gradient mb-1">{item.number}</div>
                  <div className="text-sm text-gray-600 font-medium">{item.label}</div>
                </div>
              ))}
            </div>

            {/* Core Values */}
            <div className="space-y-4">
              <h4 className="text-2xl font-bold text-gray-800 mb-6">What Drives Me</h4>
              {values.map((value, index) => (
                <div
                  key={index}
                  className="flex items-start space-x-4 p-4 rounded-xl hover:bg-primary-50 transition-all duration-300 group"
                >
                  <div className="text-2xl group-hover:scale-110 transition-transform duration-200">
                    {value.icon}
                  </div>
                  <div>
                    <h5 className="font-semibold text-gray-800 mb-1">{value.title}</h5>
                    <p className="text-sm text-gray-600">{value.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Fun Facts */}
        <div className={`mt-20 text-center transition-all duration-1000 delay-600 ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}>
          <h4 className="text-2xl font-bold text-gray-800 mb-8">Fun Facts About Me</h4>
          <div className="flex flex-wrap justify-center gap-4">
            {[
              '☕ Coffee enthusiast',
              '🎨 Digital art lover',
              '📚 Continuous learner',
              '🌸 Nature inspired',
              '🎵 Music while designing',
              '✨ Detail perfectionist'
            ].map((fact, index) => (
              <span
                key={index}
                className="bg-white px-4 py-2 rounded-full shadow-elegant text-gray-700 hover:shadow-elegant-lg transition-all duration-300 hover:scale-105"
              >
                {fact}
              </span>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
