'use client';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const quickLinks = [
    { name: 'Home', href: '#home' },
    { name: 'About', href: '#about' },
    { name: 'Skills', href: '#skills' },
    { name: 'Projects', href: '#projects' },
    { name: 'Contact', href: '#contact' },
  ];

  const socialLinks = [
    { name: 'LinkedIn', icon: '💼', url: 'https://linkedin.com/in/yourname' },
    { name: 'Drib<PERSON>', icon: '🎨', url: 'https://dribbble.com/yourname' },
    { name: '<PERSON>hance', icon: '🎭', url: 'https://behance.net/yourname' },
    { name: 'GitHub', icon: '💻', url: 'https://github.com/yourname' },
  ];

  const scrollToSection = (href) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <footer className="bg-gray-900 text-white relative overflow-hidden">
      {/* Background Decorations */}
      <div className="absolute top-0 left-0 w-64 h-64 bg-primary-900 rounded-full -translate-y-32 -translate-x-32 opacity-20"></div>
      <div className="absolute bottom-0 right-0 w-48 h-48 bg-accent-900 rounded-full translate-y-24 translate-x-24 opacity-20"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Brand Section */}
            <div className="lg:col-span-2">
              <div className="mb-6">
                <h3 className="text-3xl font-script font-bold text-gradient mb-4">
                  Hemini
                </h3>
                <p className="text-gray-300 text-lg leading-relaxed max-w-md">
                  Passionate UI/UX designer creating beautiful, user-centered digital experiences 
                  that solve real problems and delight users.
                </p>
              </div>
              
              {/* Social Links */}
              <div className="flex space-x-4">
                {socialLinks.map((social) => (
                  <a
                    key={social.name}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-12 h-12 bg-gray-800 rounded-full flex items-center justify-center hover:bg-gradient-primary transition-all duration-300 hover:scale-110 hover:-translate-y-1 group"
                    title={social.name}
                  >
                    <span className="text-xl group-hover:scale-110 transition-transform duration-200">
                      {social.icon}
                    </span>
                  </a>
                ))}
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="text-xl font-semibold mb-6 text-white">Quick Links</h4>
              <ul className="space-y-3">
                {quickLinks.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      onClick={(e) => {
                        e.preventDefault();
                        scrollToSection(link.href);
                      }}
                      className="text-gray-300 hover:text-primary-400 transition-colors duration-200 hover:translate-x-1 inline-block"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Contact Info */}
            <div>
              <h4 className="text-xl font-semibold mb-6 text-white">Get in Touch</h4>
              <div className="space-y-3">
                <a
                  href="mailto:<EMAIL>"
                  className="flex items-center text-gray-300 hover:text-primary-400 transition-colors duration-200 group"
                >
                  <span className="mr-3 group-hover:scale-110 transition-transform duration-200">📧</span>
                  <EMAIL>
                </a>
                <a
                  href="tel:+15551234567"
                  className="flex items-center text-gray-300 hover:text-primary-400 transition-colors duration-200 group"
                >
                  <span className="mr-3 group-hover:scale-110 transition-transform duration-200">📱</span>
                  +****************
                </a>
                <div className="flex items-center text-gray-300">
                  <span className="mr-3">📍</span>
                  Your City, Country
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-400 text-sm mb-4 md:mb-0">
              © {currentYear} Hemini. All rights reserved. Made with 💖 and lots of ☕
            </div>
            
            {/* Back to Top Button */}
            <button
              onClick={scrollToTop}
              className="flex items-center space-x-2 text-gray-400 hover:text-primary-400 transition-all duration-200 hover:scale-105 group"
            >
              <span className="text-sm">Back to top</span>
              <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center group-hover:bg-primary-600 transition-all duration-200">
                <svg className="w-4 h-4 group-hover:-translate-y-0.5 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                </svg>
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2">
        <div className="w-1 h-8 bg-gradient-primary opacity-50"></div>
      </div>
    </footer>
  );
};

export default Footer;
