'use client';

import { useState, useEffect, useRef } from 'react';

const Skills = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [activeCategory, setActiveCategory] = useState('design');
  const sectionRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  const skillCategories = {
    design: {
      title: 'UI/UX Design',
      icon: '🎨',
      skills: [
        { name: 'User Research', level: 90, icon: '🔍' },
        { name: 'Wireframing', level: 95, icon: '📐' },
        { name: 'Prototyping', level: 88, icon: '⚡' },
        { name: 'Visual Design', level: 92, icon: '🎭' },
        { name: 'Interaction Design', level: 85, icon: '✨' },
        { name: 'Usability Testing', level: 87, icon: '🧪' },
      ]
    },
    tools: {
      title: 'Design Tools',
      icon: '🛠️',
      skills: [
        { name: 'Figma', level: 95, icon: '🎯' },
        { name: 'Adobe XD', level: 88, icon: '💎' },
        { name: 'Sketch', level: 82, icon: '✏️' },
        { name: 'Adobe Creative Suite', level: 90, icon: '🎨' },
        { name: 'Principle', level: 78, icon: '🔄' },
        { name: 'InVision', level: 85, icon: '👁️' },
      ]
    },
    development: {
      title: 'Development',
      icon: '💻',
      skills: [
        { name: 'HTML/CSS', level: 85, icon: '🌐' },
        { name: 'JavaScript', level: 75, icon: '⚡' },
        { name: 'React', level: 70, icon: '⚛️' },
        { name: 'Tailwind CSS', level: 88, icon: '🎨' },
        { name: 'Git/GitHub', level: 80, icon: '📚' },
        { name: 'Responsive Design', level: 92, icon: '📱' },
      ]
    },
    soft: {
      title: 'Soft Skills',
      icon: '💖',
      skills: [
        { name: 'Communication', level: 95, icon: '💬' },
        { name: 'Problem Solving', level: 90, icon: '🧩' },
        { name: 'Creativity', level: 98, icon: '🌟' },
        { name: 'Team Collaboration', level: 92, icon: '🤝' },
        { name: 'Time Management', level: 88, icon: '⏰' },
        { name: 'Adaptability', level: 90, icon: '🔄' },
      ]
    }
  };

  const SkillBar = ({ skill, index, isVisible }) => (
    <div className="mb-6 group">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <span className="text-lg group-hover:scale-110 transition-transform duration-200">
            {skill.icon}
          </span>
          <span className="font-medium text-gray-800">{skill.name}</span>
        </div>
        <span className="text-sm font-semibold text-primary-600">{skill.level}%</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
        <div
          className="h-full bg-gradient-primary rounded-full transition-all duration-1000 ease-out"
          style={{
            width: isVisible ? `${skill.level}%` : '0%',
            transitionDelay: `${index * 100}ms`
          }}
        ></div>
      </div>
    </div>
  );

  return (
    <section id="skills" ref={sectionRef} className="py-20 gradient-elegant relative overflow-hidden">
      {/* Background Decorations */}
      <div className="absolute top-20 right-10 w-32 h-32 bg-primary-200 rounded-full opacity-30 animate-float"></div>
      <div className="absolute bottom-20 left-10 w-24 h-24 bg-accent-200 rounded-full opacity-40 animate-float" style={{ animationDelay: '1s' }}></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className={`text-center mb-16 transition-all duration-1000 ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}>
          <h2 className="text-4xl md:text-5xl font-bold text-gradient mb-4">
            Skills & Expertise
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            A comprehensive toolkit for creating exceptional digital experiences
          </p>
        </div>

        {/* Category Tabs */}
        <div className={`flex flex-wrap justify-center mb-12 transition-all duration-1000 delay-200 ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}>
          {Object.entries(skillCategories).map(([key, category]) => (
            <button
              key={key}
              onClick={() => setActiveCategory(key)}
              className={`flex items-center space-x-2 px-6 py-3 m-2 rounded-full font-semibold transition-all duration-300 ${
                activeCategory === key
                  ? 'bg-gradient-primary text-white shadow-glow scale-105'
                  : 'bg-white text-gray-700 hover:bg-primary-50 hover:text-primary-600 shadow-elegant hover:shadow-elegant-lg'
              }`}
            >
              <span className="text-lg">{category.icon}</span>
              <span>{category.title}</span>
            </button>
          ))}
        </div>

        {/* Skills Content */}
        <div className={`transition-all duration-500 ${
          isVisible ? 'opacity-100' : 'opacity-0'
        }`}>
          <div className="bg-white rounded-3xl shadow-elegant-lg p-8 md:p-12">
            <div className="text-center mb-8">
              <div className="text-6xl mb-4">{skillCategories[activeCategory].icon}</div>
              <h3 className="text-3xl font-bold text-gray-800 mb-2">
                {skillCategories[activeCategory].title}
              </h3>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {skillCategories[activeCategory].skills.map((skill, index) => (
                <SkillBar
                  key={skill.name}
                  skill={skill}
                  index={index}
                  isVisible={isVisible && activeCategory}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Additional Info */}
        <div className={`mt-16 text-center transition-all duration-1000 delay-600 ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}>
          <div className="bg-white rounded-2xl shadow-elegant p-8 max-w-4xl mx-auto">
            <h4 className="text-2xl font-bold text-gray-800 mb-6">My Design Philosophy</h4>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center group">
                <div className="text-4xl mb-3 group-hover:scale-110 transition-transform duration-200">🎯</div>
                <h5 className="font-semibold text-gray-800 mb-2">Purpose-Driven</h5>
                <p className="text-sm text-gray-600">Every design element serves a purpose and enhances user experience</p>
              </div>
              <div className="text-center group">
                <div className="text-4xl mb-3 group-hover:scale-110 transition-transform duration-200">🌟</div>
                <h5 className="font-semibold text-gray-800 mb-2">Innovative</h5>
                <p className="text-sm text-gray-600">Pushing boundaries while maintaining usability and accessibility</p>
              </div>
              <div className="text-center group">
                <div className="text-4xl mb-3 group-hover:scale-110 transition-transform duration-200">💝</div>
                <h5 className="font-semibold text-gray-800 mb-2">Empathetic</h5>
                <p className="text-sm text-gray-600">Understanding user needs and emotions to create meaningful connections</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Skills;
